#!/usr/bin/env python3
"""
脚本用于比较封面图片与视频首帧的相似度，判断封面是否为视频首帧
"""

import os
import sys
import cv2
import numpy as np
from pathlib import Path
import pandas as pd
from PIL import Image
import argparse
from tqdm import tqdm
import json
from datetime import datetime
from skimage.metrics import structural_similarity as ssim
import hashlib

def extract_first_frame(video_path):
    """
    提取视频的第一帧
    
    Args:
        video_path (str): 视频文件路径
        
    Returns:
        numpy.ndarray: 第一帧图像，如果失败返回None
    """
    try:
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            return None
            
        ret, frame = cap.read()
        cap.release()
        
        if ret:
            return frame
        else:
            return None
    except Exception as e:
        print(f"提取视频首帧失败 {video_path}: {e}")
        return None

def load_image(image_path):
    """
    加载图片文件
    
    Args:
        image_path (str): 图片文件路径
        
    Returns:
        numpy.ndarray: 图像数组，如果失败返回None
    """
    try:
        # 使用PIL加载图片，然后转换为OpenCV格式
        pil_image = Image.open(image_path)
        # 转换为RGB（如果是RGBA等格式）
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')
        # 转换为numpy数组并调整颜色通道顺序（PIL是RGB，OpenCV是BGR）
        image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        return image
    except Exception as e:
        print(f"加载图片失败 {image_path}: {e}")
        return None

def resize_images_to_same_size(img1, img2):
    """
    将两张图片调整为相同尺寸
    
    Args:
        img1, img2: 两张图片的numpy数组
        
    Returns:
        tuple: 调整后的两张图片
    """
    # 获取两张图片的尺寸
    h1, w1 = img1.shape[:2]
    h2, w2 = img2.shape[:2]
    
    # 选择较小的尺寸作为目标尺寸
    target_h = min(h1, h2)
    target_w = min(w1, w2)
    
    # 调整图片尺寸
    img1_resized = cv2.resize(img1, (target_w, target_h))
    img2_resized = cv2.resize(img2, (target_w, target_h))
    
    return img1_resized, img2_resized

def calculate_similarity_metrics(img1, img2):
    """
    计算两张图片的相似度指标
    
    Args:
        img1, img2: 两张图片的numpy数组
        
    Returns:
        dict: 包含各种相似度指标的字典
    """
    # 确保图片尺寸相同
    img1, img2 = resize_images_to_same_size(img1, img2)
    
    # 转换为灰度图
    gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
    gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
    
    metrics = {}
    
    # 1. 结构相似性指数 (SSIM)
    try:
        ssim_score = ssim(gray1, gray2)
        metrics['ssim'] = ssim_score
    except Exception as e:
        metrics['ssim'] = None
        print(f"SSIM计算失败: {e}")
    
    # 2. 均方误差 (MSE)
    try:
        mse = np.mean((gray1.astype(float) - gray2.astype(float)) ** 2)
        metrics['mse'] = mse
    except Exception as e:
        metrics['mse'] = None
        print(f"MSE计算失败: {e}")
    
    # 3. 峰值信噪比 (PSNR)
    try:
        if metrics['mse'] is not None and metrics['mse'] > 0:
            psnr = 20 * np.log10(255.0 / np.sqrt(metrics['mse']))
            metrics['psnr'] = psnr
        else:
            metrics['psnr'] = float('inf') if metrics['mse'] == 0 else None
    except Exception as e:
        metrics['psnr'] = None
        print(f"PSNR计算失败: {e}")
    
    # 4. 直方图相关性
    try:
        hist1 = cv2.calcHist([gray1], [0], None, [256], [0, 256])
        hist2 = cv2.calcHist([gray2], [0], None, [256], [0, 256])
        hist_corr = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
        metrics['histogram_correlation'] = hist_corr
    except Exception as e:
        metrics['histogram_correlation'] = None
        print(f"直方图相关性计算失败: {e}")
    
    # 5. 感知哈希相似度
    try:
        # 计算感知哈希
        def perceptual_hash(image):
            # 调整大小为8x8
            resized = cv2.resize(image, (8, 8))
            # 计算平均值
            avg = resized.mean()
            # 生成哈希
            hash_bits = resized > avg
            return hash_bits.flatten()
        
        hash1 = perceptual_hash(gray1)
        hash2 = perceptual_hash(gray2)
        # 计算汉明距离
        hamming_distance = np.sum(hash1 != hash2)
        # 转换为相似度（0-1，1表示完全相同）
        hash_similarity = 1 - (hamming_distance / 64.0)
        metrics['perceptual_hash_similarity'] = hash_similarity
    except Exception as e:
        metrics['perceptual_hash_similarity'] = None
        print(f"感知哈希计算失败: {e}")
    
    return metrics

def is_likely_first_frame(metrics, thresholds=None):
    """
    根据相似度指标判断封面是否可能是视频首帧
    
    Args:
        metrics (dict): 相似度指标字典
        thresholds (dict): 阈值设置
        
    Returns:
        dict: 判断结果
    """
    if thresholds is None:
        thresholds = {
            'ssim_threshold': 0.8,  # SSIM > 0.8 认为相似
            'mse_threshold': 1000,  # MSE < 1000 认为相似
            'psnr_threshold': 20,   # PSNR > 20 认为相似
            'hist_corr_threshold': 0.8,  # 直方图相关性 > 0.8 认为相似
            'hash_sim_threshold': 0.8,   # 感知哈希相似度 > 0.8 认为相似
        }
    
    results = {}
    votes = 0
    total_metrics = 0
    
    # SSIM判断
    if metrics.get('ssim') is not None:
        total_metrics += 1
        if metrics['ssim'] >= thresholds['ssim_threshold']:
            votes += 1
            results['ssim_match'] = True
        else:
            results['ssim_match'] = False
    
    # MSE判断
    if metrics.get('mse') is not None:
        total_metrics += 1
        if metrics['mse'] <= thresholds['mse_threshold']:
            votes += 1
            results['mse_match'] = True
        else:
            results['mse_match'] = False
    
    # PSNR判断
    if metrics.get('psnr') is not None and metrics['psnr'] != float('inf'):
        total_metrics += 1
        if metrics['psnr'] >= thresholds['psnr_threshold']:
            votes += 1
            results['psnr_match'] = True
        else:
            results['psnr_match'] = False
    
    # 直方图相关性判断
    if metrics.get('histogram_correlation') is not None:
        total_metrics += 1
        if metrics['histogram_correlation'] >= thresholds['hist_corr_threshold']:
            votes += 1
            results['hist_corr_match'] = True
        else:
            results['hist_corr_match'] = False
    
    # 感知哈希相似度判断
    if metrics.get('perceptual_hash_similarity') is not None:
        total_metrics += 1
        if metrics['perceptual_hash_similarity'] >= thresholds['hash_sim_threshold']:
            votes += 1
            results['hash_sim_match'] = True
        else:
            results['hash_sim_match'] = False
    
    # 综合判断：超过一半的指标认为相似
    if total_metrics > 0:
        confidence = votes / total_metrics
        results['is_likely_first_frame'] = votes > total_metrics / 2
        results['confidence'] = confidence
        results['votes'] = votes
        results['total_metrics'] = total_metrics
    else:
        results['is_likely_first_frame'] = False
        results['confidence'] = 0.0
        results['votes'] = 0
        results['total_metrics'] = 0
    
    return results

def find_matching_files(downloads_dir):
    """
    在下载目录中找到匹配的封面和视频文件对
    
    Args:
        downloads_dir (str): 下载目录路径
        
    Returns:
        list: 匹配的文件对列表
    """
    downloads_path = Path(downloads_dir)
    if not downloads_path.exists():
        print(f"下载目录不存在: {downloads_dir}")
        return []
    
    # 获取所有文件
    all_files = list(downloads_path.glob("*"))
    
    # 分类文件
    cover_files = {}  # ctt_id -> cover_path
    video_files = {}  # ctt_id -> video_path
    
    for file_path in all_files:
        filename = file_path.name
        
        # 解析文件名格式: {ctt_id}_{file_id}_{type}.{ext}
        if '_cover.jpg' in filename:
            ctt_id = filename.split('_')[0]
            cover_files[ctt_id] = file_path
        elif '_video.mp4' in filename:
            ctt_id = filename.split('_')[0]
            video_files[ctt_id] = file_path
    
    # 找到匹配的对
    matching_pairs = []
    for ctt_id in cover_files:
        if ctt_id in video_files:
            matching_pairs.append({
                'ctt_id': ctt_id,
                'cover_path': cover_files[ctt_id],
                'video_path': video_files[ctt_id]
            })
    
    return matching_pairs

def analyze_cover_video_pairs(downloads_dir, output_file=None, sample_size=None):
    """
    分析封面和视频文件对，判断封面是否为视频首帧
    
    Args:
        downloads_dir (str): 下载目录路径
        output_file (str): 输出结果文件路径
        sample_size (int): 分析样本数量限制
    """
    print("正在查找匹配的封面和视频文件对...")
    matching_pairs = find_matching_files(downloads_dir)
    
    if not matching_pairs:
        print("未找到匹配的封面和视频文件对")
        return
    
    print(f"找到 {len(matching_pairs)} 对匹配的文件")
    
    # 限制样本数量
    if sample_size and sample_size < len(matching_pairs):
        matching_pairs = matching_pairs[:sample_size]
        print(f"限制分析样本数量为: {sample_size}")
    
    results = []
    
    # 分析每一对文件
    with tqdm(total=len(matching_pairs), desc="分析进度", unit="对") as pbar:
        for pair in matching_pairs:
            ctt_id = pair['ctt_id']
            cover_path = pair['cover_path']
            video_path = pair['video_path']
            
            pbar.set_description(f"分析 {ctt_id[:20]}...")
            
            # 加载封面图片
            cover_image = load_image(cover_path)
            if cover_image is None:
                results.append({
                    'ctt_id': ctt_id,
                    'cover_path': str(cover_path),
                    'video_path': str(video_path),
                    'error': '无法加载封面图片'
                })
                pbar.update(1)
                continue
            
            # 提取视频首帧
            first_frame = extract_first_frame(video_path)
            if first_frame is None:
                results.append({
                    'ctt_id': ctt_id,
                    'cover_path': str(cover_path),
                    'video_path': str(video_path),
                    'error': '无法提取视频首帧'
                })
                pbar.update(1)
                continue
            
            # 计算相似度指标
            try:
                metrics = calculate_similarity_metrics(cover_image, first_frame)
                judgment = is_likely_first_frame(metrics)
                
                result = {
                    'ctt_id': ctt_id,
                    'cover_path': str(cover_path),
                    'video_path': str(video_path),
                    'metrics': metrics,
                    'judgment': judgment,
                    'is_likely_first_frame': judgment['is_likely_first_frame'],
                    'confidence': judgment['confidence']
                }
                results.append(result)
                
            except Exception as e:
                results.append({
                    'ctt_id': ctt_id,
                    'cover_path': str(cover_path),
                    'video_path': str(video_path),
                    'error': f'分析失败: {str(e)}'
                })
            
            pbar.update(1)
    
    # 统计结果
    successful_analyses = [r for r in results if 'error' not in r]
    likely_first_frame = [r for r in successful_analyses if r['is_likely_first_frame']]
    
    print(f"\n=== 分析完成 ===")
    print(f"总分析对数: {len(matching_pairs)}")
    print(f"成功分析: {len(successful_analyses)}")
    print(f"可能是首帧的封面: {len(likely_first_frame)}")
    print(f"首帧比例: {len(likely_first_frame)/len(successful_analyses)*100:.1f}%" if successful_analyses else "0%")
    
    # 保存结果
    if output_file:
        output_data = {
            'analysis_time': datetime.now().isoformat(),
            'total_pairs': len(matching_pairs),
            'successful_analyses': len(successful_analyses),
            'likely_first_frame_count': len(likely_first_frame),
            'results': results
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        print(f"结果已保存到: {output_file}")
    
    return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='比较封面图片与视频首帧的相似度')
    parser.add_argument('downloads_dir', help='下载目录路径')
    parser.add_argument('-o', '--output', help='输出结果文件路径 (JSON格式)')
    parser.add_argument('-n', '--sample-size', type=int, help='分析样本数量限制')
    
    args = parser.parse_args()
    
    # 检查下载目录是否存在
    if not os.path.exists(args.downloads_dir):
        print(f"错误: 下载目录不存在: {args.downloads_dir}")
        sys.exit(1)
    
    # 开始分析
    analyze_cover_video_pairs(args.downloads_dir, args.output, args.sample_size)

if __name__ == "__main__":
    main()
