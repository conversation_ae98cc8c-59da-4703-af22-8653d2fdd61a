# 封面首帧分析工具

这些工具用于判断下载的封面图片是否为对应视频的第一帧。

## 文件说明

1. **`compare_cover_with_first_frame.py`** - 完整版分析工具
   - 使用多种相似度指标 (SSIM, MSE, PSNR, 直方图相关性, 感知哈希)
   - 提供详细的分析结果和置信度
   - 支持JSON格式输出结果

2. **`quick_first_frame_check.py`** - 快速检测工具
   - 使用直方图相关性进行快速检测
   - 适合大批量数据的初步筛选
   - 运行速度更快，资源消耗更少

3. **`requirements_analysis.txt`** - 依赖包列表

## 安装依赖

```bash
pip install -r requirements_analysis.txt
```

## 使用方法

### 1. 快速批量检测 (推荐)

检测下载目录中所有的封面-视频对：

```bash
python quick_first_frame_check.py downloads/cover_pairs
```

限制检测数量（用于测试）：

```bash
python quick_first_frame_check.py downloads/cover_pairs -n 100
```

调整相似度阈值：

```bash
python quick_first_frame_check.py downloads/cover_pairs -t 0.7
```

### 2. 单个文件检测

检测特定的封面和视频文件：

```bash
python quick_first_frame_check.py cover.jpg -v video.mp4
```

### 3. 详细分析 (完整版)

进行详细的相似度分析：

```bash
python compare_cover_with_first_frame.py downloads/cover_pairs
```

保存详细结果到JSON文件：

```bash
python compare_cover_with_first_frame.py downloads/cover_pairs -o analysis_results.json
```

限制分析样本数量：

```bash
python compare_cover_with_first_frame.py downloads/cover_pairs -n 50 -o results.json
```

## 相似度指标说明

### 快速检测工具使用的指标：

- **直方图相关性**: 比较两张图片的灰度分布，范围0-1，1表示完全相同

### 完整分析工具使用的指标：

1. **SSIM (结构相似性指数)**: 考虑亮度、对比度和结构的综合相似性
2. **MSE (均方误差)**: 像素级别的差异，值越小越相似
3. **PSNR (峰值信噪比)**: 基于MSE的图像质量指标，值越大越相似
4. **直方图相关性**: 灰度分布的相似性
5. **感知哈希相似度**: 基于图像感知特征的相似性

## 判断阈值建议

- **相似度 ≥ 0.9**: 很可能是首帧 (高度相似)
- **相似度 ≥ 0.7**: 可能是首帧 (中等相似)
- **相似度 ≥ 0.5**: 有一定相似性
- **相似度 < 0.5**: 不太可能是首帧

## 输出示例

### 快速检测输出：
```
找到 150 对文件
开始检测 (相似度阈值: 0.8)...
检测进度: 100%|████████████| 150/150

=== 检测完成 ===
总检测数量: 150
封面是首帧的数量: 45
首帧比例: 30.0%

=== 相似度最高的前5个 ===
1. abc123: 0.956 - ✓ 是首帧
2. def456: 0.923 - ✓ 是首帧
3. ghi789: 0.887 - ✓ 是首帧
...
```

### 单文件检测输出：
```
检测封面: cover.jpg
检测视频: video.mp4

相似度分数: 0.923
结论: 封面很可能是视频首帧 (高度相似)
```

## 注意事项

1. **文件命名格式**: 工具假设文件按照以下格式命名：
   - 封面: `{ctt_id}_{cover_id}_cover.jpg`
   - 视频: `{ctt_id}_{video_id}_video.mp4`

2. **性能考虑**: 
   - 快速检测工具适合大批量数据
   - 完整分析工具提供更准确的结果但速度较慢

3. **准确性**: 
   - 相似度检测基于图像特征，可能受到压缩、尺寸调整等因素影响
   - 建议结合多个指标进行综合判断

4. **视频格式**: 目前支持常见的视频格式 (mp4, avi, mov等)

## 故障排除

如果遇到问题：

1. 确保安装了所有依赖包
2. 检查文件路径和命名格式
3. 确认视频文件可以正常播放
4. 检查图片文件是否损坏

## 扩展使用

你可以根据需要调整相似度阈值和算法参数，或者集成到你的数据处理流程中。
